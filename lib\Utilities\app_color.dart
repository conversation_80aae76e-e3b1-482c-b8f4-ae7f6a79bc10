import 'package:flutter/material.dart';

class AppColor {
  AppColor._();
  static const Color primary = Color(0xFFE53935);

  // Alias for primaryColor (commonly used in UI components)
  static const Color primaryColor = primary;

  //white
  static const Color white = Colors.white;
  static const Color white2 = Color(0xFFF1F5EC);

  //black
  static const Color black = Colors.black;
  static const Color blackText = Color(0XFF002106);
  static const Color lightBlack = Colors.black87;
  static const Color blackSubtext = Color(0xFF363C3C);

  //grey
  static const Color grey = Color(0xFF585C5C);
  static const Color lightGrey = Color(0xFFE7E7E7);

  //green
  static const Color greenBullet = Color(0xFF009959);
  static const Color lightGreen = Color(0xFF7BC778);

  //light_red
  static const Color lightred = Color(0xFFFEE2E2);

  //blue
  static const Color blue1 = Color(0xFFDAEDFF);
  static const Color blue2 = Color(0xFF0A51BC);

  //orange
  static const Color orange = Color(0xFFE5BA23);
  static const Color lightOrange = Color(0xFFF9F4C8);
}
