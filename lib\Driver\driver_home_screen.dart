import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/dashboard_screen.dart';
import 'package:sepesha_app/Driver/history/presentation/history_screen.dart';
import 'package:sepesha_app/Driver/account/driver_account_screen.dart';
import 'package:sepesha_app/provider/payment_provider.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const HistoryScreen(),
    const DriverAccountScreen(), 
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false, 
        title: Text(_getTitleForIndex(_currentIndex)),
        actions: _getActionsForIndex(_currentIndex),
      ),

      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'Trips',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person), 
            label: 'Account',
          ),
        ],
      ),
    );
  }

  String _getTitleForIndex(int index) {
    switch (index) {
      case 0: return 'Driver Dashboard';
      case 1: return 'Trip History';
      case 2: return 'Account';
      default: return 'Driver';
    }
  }

  List<Widget> _getActionsForIndex(int index) {
    switch (index) {
      case 0: // Dashboard
        return [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              setState(() => _currentIndex = 1); // Switch to history tab
            },
          ),
        ];
      case 1: // History
        return [];
      case 2: // Account
        return [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Handle notifications
            },
          ),
        ];
      default:
        return [];
    }
  }

  // Remove all the drawer-related methods (_buildDrawer, _showLogoutDialog, etc.)
  // They're now handled in DriverAccountScreen
}